"use client";

import { useRef, useCallback } from "react";
import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import revalidateTag from "@/utils/revalidate-tag";

// Toast configurations
const success = {
  style: {
    background: "#10B981",
    color: "#FFFFFF",
    border: "none",
  },
};

const failed = {
  style: {
    background: "#EF4444",
    color: "#FFFFFF",
    border: "none",
  },
};

// Request deduplication and debouncing utilities
const activeRequests = new Map<string, Promise<any>>();
const debounceTimers = new Map<string, NodeJS.Timeout>();

/**
 * Creates a debounced function that delays invoking func until after wait milliseconds
 * have elapsed since the last time the debounced function was invoked.
 * @param {T} func - The function to debounce
 * @param {number} wait - The number of milliseconds to delay
 * @param {string} key - Unique key for this debounced function
 * @return {Function} Returns the new debounced function
 */
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  key: string
): (...args: Parameters<T>) => void {
  return (...args: Parameters<T>) => {
    // Clear existing timer for this key
    const existingTimer = debounceTimers.get(key);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    // Set new timer
    const timer = setTimeout(() => {
      debounceTimers.delete(key);
      func(...args);
    }, wait);

    debounceTimers.set(key, timer);
  };
}

/**
 * Deep comparison function to check if two objects are equal
 * @param {any} obj1 - First object to compare
 * @param {any} obj2 - Second object to compare
 * @return {boolean} True if objects are deeply equal, false otherwise
 */
function deepEqual(obj1: any, obj2: any): boolean {
  if (obj1 === obj2) return true;

  if (obj1 == null || obj2 == null) return false;

  if (typeof obj1 !== typeof obj2) return false;

  if (typeof obj1 !== "object") return obj1 === obj2;

  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  if (keys1.length !== keys2.length) return false;

  for (const key of keys1) {
    if (!keys2.includes(key)) return false;
    if (!deepEqual(obj1[key], obj2[key])) return false;
  }

  return true;
}

/**
 * Custom hook for updating notification settings with comprehensive API optimization
 * Features:
 * - 400ms debouncing to prevent excessive API calls
 * - Request deduplication to prevent multiple concurrent requests
 * - Change detection to only send requests when data actually changes
 * - Exponential backoff retry logic
 * - Proper loading state management
 * - User-friendly error handling
 * @return {Object} React Query mutation object with optimized API calls
 */
export const useUpdateNotificationSettings = () => {
  const lastDataRef = useRef<INotificationPreferences | null>(null);
  const pendingRequestRef = useRef<Promise<any> | null>(null);

  // Create debounced mutation function with proper structure
  const debouncedMutate = useCallback(
    (
      data: INotificationPreferences,
      resolve: (value: any) => void,
      reject: (error: any) => void
    ) => {
      const debouncedFunction = debounce(
        async (
          data: INotificationPreferences,
          resolve: (value: any) => void,
          reject: (error: any) => void
        ) => {
          try {
            // Change detection - only proceed if data has actually changed
            if (lastDataRef.current && deepEqual(lastDataRef.current, data)) {
              // No changes detected, skip API call to prevent unnecessary requests
              resolve({ message: "No changes to save" });
              return;
            }

            // Create a unique key for this request to prevent duplicates
            const requestKey = `update-settings-${JSON.stringify(data)}`;

            // Check if there's already an active request with the same data
            if (activeRequests.has(requestKey)) {
              // Duplicate request detected, return existing promise to prevent redundant API calls
              const existingPromise = activeRequests.get(requestKey);
              const result = await existingPromise;
              resolve(result);
              return;
            }

            // Create the request promise with timeout and abort controller
            const requestPromise = (async () => {
              try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

                const response = await fetch("/api/notifications/settings", {
                  method: "PUT",
                  headers: {
                    "Content-Type": "application/json",
                  },
                  body: JSON.stringify(data),
                  signal: controller.signal,
                });

                clearTimeout(timeoutId);

                if (!response.ok) {
                  const error = await response.json();
                  throw new Error(
                    error.message || "Failed to update notification settings"
                  );
                }

                const result = await response.json();

                // Update last data reference on successful save
                lastDataRef.current = { ...data };

                return result;
              } finally {
                // Remove from active requests when done
                activeRequests.delete(requestKey);
              }
            })();

            // Store the promise to prevent duplicate requests
            activeRequests.set(requestKey, requestPromise);

            const result = await requestPromise;
            resolve(result);
          } catch (error) {
            reject(error);
          }
        },
        400, // 400ms debounce delay
        "notification-settings-update"
      );

      // Call the debounced function
      debouncedFunction(data, resolve, reject);
    },
    []
  );

  return useMutation({
    mutationFn: async (data: INotificationPreferences) => {
      // If there's already a pending request, wait for it to complete
      if (pendingRequestRef.current) {
        try {
          await pendingRequestRef.current;
        } catch {
          // Ignore errors from previous requests
        }
      }

      // Create a new promise for this request
      const requestPromise = new Promise<any>((resolve, reject) => {
        debouncedMutate(data, resolve, reject);
      });

      pendingRequestRef.current = requestPromise;

      try {
        const result = await requestPromise;
        return result;
      } finally {
        // Clear pending request reference
        if (pendingRequestRef.current === requestPromise) {
          pendingRequestRef.current = null;
        }
      }
    },
    onSuccess: (data) => {
      // Only show success toast if there were actual changes
      if (data?.message !== "No changes to save") {
        toast.success("Notification settings updated successfully", {
          description: "Your notification preferences have been saved",
          ...success,
        });
      }
      revalidateTag(["notification-settings"]);
    },
    onError: (error: any) => {
      // Handle different types of errors with user-friendly messages
      let errorMessage = "Failed to update notification settings";
      let errorDescription = "Please try again later";

      if (error.name === "AbortError") {
        errorMessage = "Request timed out";
        errorDescription =
          "The request took too long. Please check your connection and try again.";
      } else if (error.message) {
        if (
          error.message.includes("network") ||
          error.message.includes("fetch")
        ) {
          errorMessage = "Network error";
          errorDescription =
            "Please check your internet connection and try again.";
        } else if (
          error.message.includes("unauthorized") ||
          error.message.includes("401")
        ) {
          errorMessage = "Session expired";
          errorDescription = "Please refresh the page and log in again.";
        } else {
          errorMessage = error.message;
        }
      }

      toast.error(errorMessage, {
        description: errorDescription,
        ...failed,
      });
    },
    // Retry configuration with exponential backoff
    retry: (failureCount, error) => {
      // Don't retry on client errors (4xx) or abort errors
      if (
        error.name === "AbortError" ||
        (error.status >= 400 && error.status < 500)
      ) {
        return false;
      }
      // Retry up to 2 times for server errors with exponential backoff
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff: 1s, 2s, max 30s
  });
};
